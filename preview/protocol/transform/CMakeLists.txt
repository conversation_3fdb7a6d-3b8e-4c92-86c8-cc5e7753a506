# Protocol Transform Layer CMakeLists.txt

# 创建 protocol_transform 动态库
add_library(protocol_transform SHARED
    src/protocol_transform.cpp
    src/gw104/gw104_transform.cpp
    src/gw104/asdu_type1_gws.cpp
    src/gw104/asdu_type2_gws.cpp
    src/gw104/asdu_type3_gws.cpp
    src/gw104/asdu_type4_gws.cpp
)

# 链接必要的库
target_link_libraries(protocol_transform
    PRIVATE
        core
)

# 设置包含目录
target_include_directories(protocol_transform
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 设置输出目录和版本
set_target_properties(protocol_transform PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/protocol"
    VERSION 1.0.0
    SOVERSION 1
)