/**
 * @file structs.hpp
 * @brief 结构体定义
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_STRUCTS_HPP
#define ZEXUAN_BASE_STRUCTS_HPP

#include "basic_types.hpp"
#include "enums.hpp"
#include <chrono>
#include <memory>
#include <optional>
#include <string>
#include <vector>
#include <concepts>

namespace zexuan {
namespace base {

// 前向声明
class RegisterObject;

// ============================================================================
// 结构体定义
// ============================================================================

/// @brief 设备UUID结构
struct DeviceUUID {
    DeviceId device_id{INVALID_ID};
    DeviceCategory category{DeviceCategory::UNKNOWN};

    /// @brief 默认构造函数
    DeviceUUID() = default;

    /// @brief 带参构造函数
    DeviceUUID(DeviceId id, DeviceCategory cat) noexcept
        : device_id(id), category(cat) {}

    /// @brief 比较运算符（用于 std::map）
    auto operator<=>(const DeviceUUID& other) const noexcept = default;

    /// @brief 相等运算符
    bool operator==(const DeviceUUID& other) const noexcept = default;
};

/// @brief 通用消息结构
struct CommonMessage {
    MessageType type{MessageType::COMMAND};
    ObjectId source_id{INVALID_ID};
    ObjectId target_id{INVALID_ID};
    std::string invoke_id;
    std::vector<uint8_t> data;
    bool b_lastmsg{true};  // 是否为最后一帧消息

    /// @brief 默认构造函数
    CommonMessage() = default;
};

/// @brief 事件消息结构
struct EventMessage {
    EventType event_type{0};
    DeviceUUID device_uuid;
    ObjectId source_id{INVALID_ID};
    std::string description;
    std::vector<uint8_t> data;

    /// @brief 默认构造函数
    EventMessage() = default;
};

/// @brief 协议帧结构（参考原始 PRO_FRAME_BODY，支持多帧合并）
struct ProtocolFrame {
    // 原始协议数据
    std::vector<uint8_t> data;

    // 多帧合并必要字段（参考原始 PRO_FRAME_BODY）
    uint8_t type = 0;           // 类型标识 (nType/ASDU类型)
    uint8_t vsq = 0;            // 可变结构限定词 (nVsq) - 用于判断多帧
    uint8_t cot = 0;            // 传送原因 (nCot) - 用于判断帧类型
    uint16_t asdu_addr = 0;     // ASDU地址 (nSubstationAdd) - 用于帧分组
    uint16_t addr = 0;          // 装置地址 (nAddr)
    uint8_t cpu = 0;            // CPU号 (nCpu)
    uint8_t zone = 0;           // 区域号 (nZone)
    uint8_t fun = 0;            // 功能码 (nFun)
    uint8_t inf = 0;            // 信息码 (nInf)
    uint8_t rii = 0;            // 返回信息标识符 (nRii)
    uint8_t group = 0;          // 组号 (nGroup)
    uint8_t kod = 0;            // KOD (nKod)

    // 帧管理字段
    uint32_t frame_id = 0;      // 帧ID
    bool is_last_frame = true;  // 是否最后一帧（用于多帧合并）
    std::chrono::system_clock::time_point timestamp;

    ProtocolFrame() : timestamp(std::chrono::system_clock::now()) {}
};

/// @brief 协议帧列表类型定义
using ProtocolFrameList = std::vector<ProtocolFrame>;

/// @brief 注册对象信息结构
struct RegisterObjectInfo {
    ObjectId object_id{INVALID_ID};
    std::string description;
    std::optional<std::vector<EventType>> event_types;
    std::optional<std::vector<DeviceUUID>> devices;

    /// @brief 设置对象指针（使用 concepts 约束）
    template<typename T>
    requires std::derived_from<T, RegisterObject>
    void set_object_ptr(std::shared_ptr<T> ptr) noexcept {
        object_ptr_ = std::static_pointer_cast<RegisterObject>(ptr);
    }

    /// @brief 获取对象指针
    std::shared_ptr<RegisterObject> get_object_ptr() const noexcept {
        return object_ptr_.lock();
    }

    /// @brief 检查是否设置了事件类型
    bool has_events() const noexcept {
        return event_types.has_value();
    }

    /// @brief 检查是否设置了设备列表
    bool has_devices() const noexcept {
        return devices.has_value();
    }

private:
    std::weak_ptr<RegisterObject> object_ptr_;
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_STRUCTS_HPP
