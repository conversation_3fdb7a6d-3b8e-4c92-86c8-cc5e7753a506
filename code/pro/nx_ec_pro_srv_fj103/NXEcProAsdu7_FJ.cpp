/**********************************************************************
* NXEcProAsdu7_FJ.cpp         author:sl     date:04/09/2014            
*---------------------------------------------------------------------
*  note: 国网104 ASDU7报文转换处理实现文件:相比较南网103,当对全站进行总招时,子站上送所有设备的通信状态和运行状态.南网只上送通信状态.                                                             
*  
**********************************************************************/

#include "NXEcProAsdu7_FJ.h"


/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu7FJ::~TNXEcProAsdu7FJ()
{

}

/***********************************************************************************
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
**********************************************************************************/
TNXEcProAsdu7FJ::TNXEcProAsdu7FJ(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu7(pSeekIns,pLogRecord)
{
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu7FJ");
}

/***********************************************************************************
* @brief         直接从本地生成结果回应，如初始化配置;--(子站总招)
* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
* @return        int 0-成功 其它失败
**********************************************************************************/
int TNXEcProAsdu7FJ::DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255]="";
	TNXEcProAsdu42* pAsdu42=new TNXEcProAsdu42(m_pModelSeek,m_pLogRecord);
	if (pAsdu42 == NULL)
	{
		RcdErrLogWithParentClass("DirectResFromLocal:做成ASDU42实例时异常.","TNXEcProAsdu7FJ");
		return EC_PRO_CVT_FAIL;
	}

	ASDU_ADDR Asdu7Addr;
	const SUBSTATION_TB* pSubStationTb = m_pModelSeek->GetSubStationBasicCfg();
	if (pSubStationTb == NULL)
	{
		delete pAsdu42;
		RcdErrLogWithParentClass("DirectResFromLocal:获取站配置信息出错","TNXEcProAsdu7FJ");
		return -1;
	}
	Asdu7Addr.nSubstationAdd=pSubStationTb->n_outaddr103;
	Asdu7Addr.nAddr=pBody->nAddr;
	Asdu7Addr.nCpu =0;
	Asdu7Addr.nZone=0;
	//确认是否是对子站进行总招
	if (0!= Asdu7Addr.nAddr)
	{
		delete pAsdu42;
		RcdErrLogWithParentClass("收到的命令,不是对全站进行总招.","TNXEcProAsdu7FJ");
		return -1;
	}
	//获取命令报文中的扫描序号
	int nScn=pBody->vVarData[0];
	
	//做成ASDU7_INFO_LIST
	INFO_OBJ_LIST lInfoObj;
	int nRet = MakeAsdu7InfoList(lInfoObj); //通信状态
	if (nRet < 0)
	{
		RcdErrLogWithParentClass("生成设备通信状态信息时出错.","TNXEcProAsdu7FJ");
	}
	else
	{
		//做成ASDU7的应答报文(42[2AH])
		pAsdu42->FormatAsdu42Body(Asdu7Addr,lInfoObj,nScn,lResult);
	}

	lInfoObj.clear();
	nRet = MakeAsdu7InfoList_runstatus(lInfoObj); //运行状态
	if (nRet < 0)
	{
		RcdErrLogWithParentClass("生成设备通信运行状态信息时出错.","TNXEcProAsdu7FJ");
	}
	else
	{
		//做成ASDU7的应答报文(42[2AH])
		pAsdu42->FormatAsdu42Body(Asdu7Addr,lInfoObj,nScn,lResult);
	}

	//【新增】定值区状态处理
	lInfoObj.clear();
	nRet = MakeAsdu7InfoList_settinggroup(lInfoObj); //定值区状态
	if (nRet < 0)
	{
		RcdErrLogWithParentClass("生成设备定值区状态信息时出错.","TNXEcProAsdu7FJ");
	}
	else
	{
		//做成ASDU7的应答报文(42[2AH])
		pAsdu42->FormatAsdu42Body(Asdu7Addr,lInfoObj,nScn,lResult);
	}

	//做成总招结束报文(08H)
	MakeAsdu8Body(Asdu7Addr,nScn,lResult);
	delete pAsdu42;
	lInfoObj.clear();

	return 0;
}
/***********************************************************************************
* @brief         转换规约信息到NX通用消息结构:做成召唤开关量NX_MSG
* @param[in]     PRO_FRAME_BODY* pCmd:规约命令
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
* @param[out]    PRO_FRAME_BODY_LIST & lBody：保存生成的规约失败回应(服务端规约有效）
* @return        >=0:成功 <0:失败
**********************************************************************************/
int TNXEcProAsdu7FJ::_CvtOneCmdToCommonMsg(IN PRO_FRAME_BODY* pCmd,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lBody)
{
	//Csg日志
	char cError[255] ="";
	sprintf(cError,"addr=%d,cpu=%d,",pCmd->nAddr,pCmd->nCpu);
	m_CsgLogDataUnit.str_dataobject = "收到主站总招命令.";
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutRun(m_CsgLogDataUnit);

	if( pCmd == NULL )
		return EC_PRO_CVT_FAIL;

	NX_COMMON_MESSAGE CommonMsg;

	// 获得地址信息
	const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(pCmd->nAddr);
	if( pIedTb == NULL )
	{
		_MakeFailedResultByCmd(pCmd,lBody);
		return EC_PRO_CVT_FAIL;
	}
	//如果下发的装置组标题包含有“状态量_硬压板”，则分开2条命令分别召唤硬压板和开关量
	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
	CommonMsg.n_msg_type  = NX_IED_CALL_HARDSTRAP_ASK;
	CommonMsg.n_obj_id    = pIedTb->n_obj_id;
	CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
	CommonMsg.n_sub_obj_id= pCmd->nCpu;
	CommonMsg.n_sub_sub_obj_id = pCmd->nZone;
	CommonMsg.n_data_src = 0;
	CommonMsg.b_lastmsg  = true;

	lMsg.push_back(CommonMsg);

	if (0 == __DBQueryGroupName(pIedTb->n_obj_id,pCmd->nCpu))
	{
		int nRealIed;
		int nRealCpu;
		int strback = (pIedTb->n_obj_id*1000)+pCmd->nCpu;
		if (0 !=__DBQueryRealIedByBack(nRealIed,nRealCpu,strback))
		{
			sprintf(cError,"_DirectResEventCfg():获取设备[IED=%d,CPU=%d] 的真实IED失败.",pIedTb->n_obj_id,pCmd->nCpu);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu21_Direct");
			/*return EC_PRO_CVT_FAIL;*/
		}
		else
		{
			CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
			CommonMsg.n_msg_type  = NX_IED_CALL_HARDSTRAP_ASK;
			CommonMsg.n_obj_id    = nRealIed;
			CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
			CommonMsg.n_sub_obj_id= nRealCpu;
			CommonMsg.n_sub_sub_obj_id = pCmd->nZone;
			CommonMsg.n_data_src = 0;
			CommonMsg.b_lastmsg  = true;

			lMsg.push_back(CommonMsg);
		}	
	}

	return EC_PRO_CVT_SUCCESS;	
}
/*****************************************************************
* @brief         从数据库中查找所有厂站的通信状态和运行状态.
* @param[out]    LIST_IED &ListIed: 二次设备IED基本信息表字段链表.
* @return        int 0-成功 其它失败
****************************************************************/
int TNXEcProAsdu7FJ::__DBQueryGroupName(int ied_obj,int ld_code)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的连个字段
	suField.str_fd_name="gt_name";			//厂站ID
	suParam.lst_fddata.push_back(suField);


	//查询表名
	suParam.lst_tablename.push_back("nx_t_ecu_gtitle_cfg");

	//查询条件1  类型
	suCondition.str_cdt_name="ied_obj";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",ied_obj);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询条件2  103地址
	suCondition.str_cdt_name="ld_code";
	sprintf(cIed_Id,"%d",ld_code);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_comtrade_list历史录波列表中,是否存在符合指定条件的记录.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"__DBQueryStnId():全站查询psrtype为%d的信息时出错[%s]",0,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu7FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);

	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryStnId():全站的ied中,psrtype为%d的配置不存在.",0);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu7FJ");
		return EC_PRO_CVT_FAIL;
	}

	RcdSet.move_to_top();
	string sValue;
	IED_TB IedInfo;
	for (int i = 0; i<nRecordNum ;i++ )
	{
		RcdSet.get_field_value(1,sValue);

		if (sValue == "状态量_硬压板")
		{
			return EC_PRO_CVT_SUCCESS;
		}

		RcdSet.move_to_next();
	}

	RcdSet.clear_result();

	return EC_PRO_CVT_FAIL;
}
/*****************************************************************
* @brief         从数据库中查找指定厂站ID.
* @param[in]     int nIed_Id:设备ID.
* @param[out]    INT nStation_Id: 厂站ID.
* @return        int 0-成功 其它失败
****************************************************************/
int TNXEcProAsdu7FJ::__DBQueryRealIed(IN int nIed_Id,IN int nld_code,OUT int & nReal)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的连个字段
	suField.str_fd_name="strbackup1";			//真实ID
	suParam.lst_fddata.push_back(suField);


	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_hardstrap_cfg");

	//查询条件1  设备ID
	suCondition.str_cdt_name="obj_id";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nIed_Id);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询条件1  设备ID
	suCondition.str_cdt_name="ld_code";
	sprintf(cIed_Id,"%d",nld_code);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_comtrade_list历史录波列表中,是否存在符合指定条件的记录.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"__DBQueryRealIed():全站查询ied为%d，ld_code为%d的信息时出错[%s]",nIed_Id,nld_code,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu7FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryRealIed():全站的ied中,ied为%d,ld_code为%d的配置不存在.",nIed_Id,nld_code);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu7FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nReal = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryRealIed():获取IED：%d,真实ID：%d.",nIed_Id,nReal);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu7FJ");
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief		生成已接入设备的信息列表:fun 0xF0,103地址(inf),dpi 最新运行状态			
* @param[out]   ASDU7_INFO_LIST& LAsdu7Info:返回信息列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu7FJ::MakeAsdu7InfoList_runstatus(OUT INFO_OBJ_LIST& lInfoObj)
{
	//获取当前接入设备列表信息.
	LIST_IED ListIed;
	ASDU_INFO_OBJ InfoObj;

	bool bRet = m_pModelSeek->GetAllIedStatus(ListIed);
	if (!bRet)
	{
		RcdErrLogWithParentClass("查找全站设备信息时,函数GetAllIedStatus出错.","TNXEcProAsdu7FJ");
		return -1;
	}

	if(ListIed.size()==0) return -1;//没有找到设备,也按失败处理.

	LIST_IED::iterator ite=ListIed.begin();
    while(ite != ListIed.end() )
	{
		
		if( ite->n_outaddr103 <= 0 )        // 对上通信无效的过滤
		{
			++ite;
			continue;
		}
		InfoObj.nFun = 251;  //FB
		InfoObj.nInf=ite->n_outaddr103;               
		switch(ite->e_opramode)//国网104:DPI=1（检修），DPI=2（正常），DPI=其他（未知）
		{		
		case OPRAMODE_RUN://		= 2,/**<  投运 */
			InfoObj.nDpi = 2;
			break;	
		case OPRAMODE_STOP:// 1,/**<  停运 */
		case OPRAMODE_NO_ACCESS://	= 3,/**<  未接入 */
		case OPRAMODE_TEST://0**<  检修 */
		case OPRAMODE_DEBUG://		= 4/**<  调试(对码表) */
		default:
			InfoObj.nDpi = 1;
			break;		
		}
		lInfoObj.push_back(InfoObj);
		++ite;
	}

	ListIed.clear();

	return 0;
}
/*****************************************************************
* @brief         根据开关量从数据库中查找指定设备ID.
* @param[in]     int nIed_Id:设备ID.
* @param[out]    INT nStation_Id: 厂站ID.
* @return        int 0-成功 其它失败
****************************************************************/
int TNXEcProAsdu7FJ::__DBQueryRealIedByBack(OUT int &obj_id,OUT int &n_field_id,IN int nReal)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的连个字段
	suField.str_fd_name="ied_obj";			//真实ID
	suParam.lst_fddata.push_back(suField);

	suField.str_fd_name="ld_code";			//真实ID
	suParam.lst_fddata.push_back(suField);


	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_hardstrap_cfg");

	//查询条件1  设备ID
	suCondition.str_cdt_name="strbackup1";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nReal);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_CHAR;
	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_comtrade_list历史录波列表中,是否存在符合指定条件的记录.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"__DBQueryRealIedByFid():全站查询strbackup1为%d的信息时出错[%s]",nReal,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu42FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryRealIedByFid():全站的ied中,strbackup1为%d的配置不存在.",nReal);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu42FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	obj_id = atoi(strValue.c_str());

	RcdSet.get_field_value_row(2,1,strValue);
	n_field_id = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryRealIedByFid():获取strbackup1：%d,真实ID：%d.CPU:%d.",nReal,obj_id,n_field_id);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu42FJ");
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief		生成已接入设备定值区信息列表:fun=250,103地址(inf),dpi=当前定值区号			
* @param[out]   OUT INFO_OBJ_LIST& lInfoObj:返回信息列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu7FJ::MakeAsdu7InfoList_settinggroup(OUT INFO_OBJ_LIST& lInfoObj)
{
	//获取当前接入设备列表信息.
	LIST_IED ListIed;
	ASDU_INFO_OBJ InfoObj;

	bool bRet = m_pModelSeek->GetAllIedStatus(ListIed);
	if (!bRet)
	{
		RcdErrLogWithParentClass("查找全站设备信息时,函数GetAllIedStatus出错.","TNXEcProAsdu7FJ");
		return -1;
	}

	if(ListIed.size()==0) return -1;//没有找到设备,也按失败处理.

	// 直接查询数据库获取定值区信息
	INXEcModelMgr * pModelMgr = m_pModelSeek->GetModelMgrObj();
	if ( NULL == pModelMgr)
	{
		RcdErrLogWithParentClass("获取模型管理器对象失败.","TNXEcProAsdu7FJ");
		return -1;
	}

	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集
	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的字段
	suField.str_fd_name="ied_obj";			//IED对象ID
	suParam.lst_fddata.push_back(suField);
	suField.str_fd_name="curvalue";			//当前定值区号
	suParam.lst_fddata.push_back(suField);

	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_sgzone_cfg");

	//查询条件：只查询当前定值区(psrtype=1)
	suCondition.str_cdt_name="psrtype";
	suCondition.str_cdt_value="1";
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询数据库
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"查询全站定值区信息时出错[%s]",cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu7FJ");
		return -1;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应记录
	{
		RcdSet.clear_result();
		RcdTrcLogWithParentClass("数据库中未找到当前定值区配置信息.","TNXEcProAsdu7FJ");
		// 即使没有定值区配置，也要为所有设备生成默认的定值区信息
	}
	else
	{
		// 创建IED ID到定值区的映射
		map<int, int> mapIedToZone;
		RcdSet.move_to_top();
		string sValue;
		for (int i = 0; i<nRecordNum ;i++ )
		{
			RcdSet.get_field_value(1,sValue);
			int nIedId = atoi(sValue.c_str());
			RcdSet.get_field_value(2,sValue);
			int nZoneValue = atoi(sValue.c_str());
			
			mapIedToZone[nIedId] = nZoneValue;
			
			char cDebug[255];
			sprintf(cDebug,"从数据库获取定值区: IED=%d, 定值区=%d", nIedId, nZoneValue);
			RcdTrcLogWithParentClass(cDebug,"TNXEcProAsdu7FJ");
			
			RcdSet.move_to_next();
		}
		RcdSet.clear_result();

		// 为所有设备生成定值区信息
		LIST_IED::iterator ite=ListIed.begin();
		while(ite != ListIed.end() )
		{
			if( ite->n_outaddr103 <= 0 )        // 对上通信无效的过滤
			{
				++ite;
				continue;
			}
			
			InfoObj.nFun = 250;  // 0xFA - 定值区功能类型
			InfoObj.nInf = ite->n_outaddr103;   // 103地址作为信息序号
			
			// 从映射中获取定值区号，如果没有则使用默认值1
			map<int, int>::iterator iteMap = mapIedToZone.find(ite->n_obj_id);
			if (iteMap != mapIedToZone.end())
			{
				InfoObj.nDpi = iteMap->second;
			}
			else
			{
				InfoObj.nDpi = 1; // 默认定值区1
				char cDebug[255];
				sprintf(cDebug,"设备[IED=%d]未找到定值区配置，使用默认定值区1", ite->n_obj_id);
				RcdTrcLogWithParentClass(cDebug,"TNXEcProAsdu7FJ");
			}
			
			// 确保DPI值在合理范围内（通常为1-4）
			if (InfoObj.nDpi < 1) InfoObj.nDpi = 1;
			if (InfoObj.nDpi > 4) InfoObj.nDpi = 4;
			
			lInfoObj.push_back(InfoObj);
			++ite;
		}
	}

	ListIed.clear();
	return 0;
}